# Implementation Plan: User Permissions API Endpoint

## Overview
Implement a new API endpoint `GET /api/v3/user/permissions` that returns all permissions available to the authenticated user, following the clean architecture pattern established in the existing user profile handler.

## Requirements Analysis

### API Specification
- **Endpoint**: `GET /api/v3/user/permissions`
- **Authentication**: JWT token required (existing middleware)
- **Response Format**: JSON matching the schema in `/workspace/schemas/document.txt`

### Expected Response Structure
```json
{
  "userId": "c3c1d5f1-7ea9-4f95-a840-30a4bb3c53a0",
  "permissions": [
    {
      "scope": "global|org|device_group|location_group",
      "scopeId": "string|null",
      "organizationId": "string|null", 
      "permissions": ["permission_identifier_1", "permission_identifier_2"]
    }
  ]
}
```

### Database Schema Understanding
The permission system uses the following key tables:
- `User` → `AuthMethod` → `Memberships` → Role Assignments
- `OrgRoleAssignments`, `DeviceGroupRoleAssignments`, `LocationGroupRoleAssignments`
- `CustomRole` → `TemplateRole` → `TemplateRolePermission` + `CustomRolePermission` overrides
- `Permission` → `PermissionGroup` (scope-based grouping)

## Implementation Plan

### 1. Create New Handler Package
**Location**: `/workspace/microservices/broker/api/handlers/v3/user/permissions/`

**Files to create**:
- `handler.go` - Main handler logic with dependency injection
- `schemas.go` - Request/response data structures  
- `errors.go` - Error definitions
- `handler_test.go` - Unit tests

### 2. Handler Implementation (`handler.go`)

**Dependencies**:
- `UserPermissionsFromContext` - Extract user from JWT (existing)
- `GetConnections` - Database connection (existing)
- `GetUserPermissions` - New function to retrieve user permissions

**Handler Logic**:
1. Extract user ID from JWT context using existing authorizer
2. Get database connection
3. Query user permissions using existing `getAllUserPermissions` function from authorizer
4. Transform permissions to match API response format
5. Return JSON response

**Key Implementation Details**:
- Reuse existing `authorizer.getAllUserPermissions()` function
- Transform `authorizer.Permission` structs to API response format
- Handle global scope permissions (if any exist)
- Map scope names: "org" → "org", "device_group" → "device_group", etc.

### 3. Response Schema (`schemas.go`)

```go
type UserPermissionsResponse struct {
    UserID      string            `json:"userId"`
    Permissions []PermissionScope `json:"permissions"`
}

type PermissionScope struct {
    Scope          string   `json:"scope"`
    ScopeID        *string  `json:"scopeId"`
    OrganizationID *string  `json:"organizationId"`
    Permissions    []string `json:"permissions"`
}
```

### 4. Error Handling (`errors.go`)

```go
var (
    ErrUserInfoRetrieve     = errors.New("unable to retrieve user info from request context")
    ErrPermissionsNotFound  = errors.New("user permissions not found")
)
```

### 5. Route Registration

**File**: `/workspace/microservices/broker/api/routes/routes.go`

Add import:
```go
V3UserPermissions "synapse-its.com/broker/api/handlers/v3/user/permissions"
```

Add route:
```go
router.HandleFunc("/api/v3/user/permissions", V3UserPermissions.Handler).Methods(http.MethodGet)
```

### 6. Testing Strategy

**Unit Tests**:
- Test successful permission retrieval
- Test user not found scenarios
- Test database connection errors
- Test permission transformation logic
- Mock dependencies for isolated testing

**Integration Tests**:
- Test with real test users from dev.sql
- Verify correct permissions returned for different user roles
- Test with users having multiple organization memberships

**Test Users Available** (from `/workspace/schemas/data-core-pg/1.0/DML/dev.sql`):
- Admin user: `550e8400-e29b-41d4-a716-446655440001` (full permissions)
- Manager user: `550e8400-e29b-41d4-a716-446655440002` (limited permissions)
- Technician user: `550e8400-e29b-41d4-a716-446655440003` (device-only permissions)
- Multi-org user: `550e8400-e29b-41d4-a716-446655440005` (multiple organizations)

### 7. Implementation Steps

1. **Create handler package structure**
   - Create directory and base files
   - Implement schemas and errors

2. **Implement core handler logic**
   - Create handler with dependency injection pattern
   - Implement permission retrieval and transformation
   - Add proper error handling

3. **Add route registration**
   - Update routes.go with new endpoint
   - Update route tests

4. **Write comprehensive tests**
   - Unit tests with mocked dependencies
   - Integration tests with test database

5. **Manual testing**
   - Test with different user types
   - Verify response format matches specification
   - Test error scenarios

### 8. Key Technical Considerations

**Reuse Existing Code**:
- Leverage `authorizer.getAllUserPermissions()` for database queries
- Use existing JWT middleware and user context extraction
- Follow established dependency injection pattern

**Performance**:
- The existing permission query is already optimized
- Consider caching if needed (future enhancement)

**Security**:
- Users can only see their own permissions (enforced by JWT)
- No additional authorization needed beyond existing JWT middleware

**Maintainability**:
- Follow existing clean architecture patterns
- Use dependency injection for testability
- Clear separation of concerns

### 9. Future Enhancements

- Add permission caching layer
- Add filtering/pagination for large permission sets
- Add permission metadata (display names, descriptions)
- Add audit logging for permission access

## Files to Modify/Create

### New Files:
- `/workspace/microservices/broker/api/handlers/v3/user/permissions/handler.go`
- `/workspace/microservices/broker/api/handlers/v3/user/permissions/schemas.go`
- `/workspace/microservices/broker/api/handlers/v3/user/permissions/errors.go`
- `/workspace/microservices/broker/api/handlers/v3/user/permissions/handler_test.go`

### Modified Files:
- `/workspace/microservices/broker/api/routes/routes.go` (add route)
- `/workspace/microservices/broker/api/routes/routes_test.go` (update test)

## Success Criteria

1. ✅ API endpoint responds with correct JSON format
2. ✅ Permissions match user's actual database assignments
3. ✅ All test users return expected permission sets
4. ✅ Error handling works correctly
5. ✅ Unit tests achieve >90% coverage
6. ✅ Integration tests pass with test database
7. ✅ Code follows existing patterns and conventions
