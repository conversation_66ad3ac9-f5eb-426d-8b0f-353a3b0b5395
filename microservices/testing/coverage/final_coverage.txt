/workspace/microservices/broker/api/brokerShared/shared.go                                      : 100.00% (13/13 statements)
/workspace/microservices/broker/api/handlers/v3/data/device/handler.go                          : 100.00% (115/115 statements)
/workspace/microservices/broker/api/handlers/v3/data/device/helper.go                           : 100.00% (14/14 statements)
/workspace/microservices/broker/api/handlers/v3/data/fault/handler.go                           : 100.00% (83/83 statements)
/workspace/microservices/broker/api/handlers/v3/data/fault/helper.go                            : 100.00% (98/98 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/authenticate/handler.go                 : 100.00% (85/85 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/authenticate/security.go                : 100.00% (22/22 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/ingest/handler.go                       : 100.00% (111/111 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/ingest/schemas.go                       : 100.00% (2/2 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/update/handler.go                       : 100.00% (79/79 statements)
/workspace/microservices/broker/api/handlers/v3/user/account/close/handler.go                   : 100.00% (40/40 statements)
/workspace/microservices/broker/api/handlers/v3/user/account/notifications/handler.go           : 100.00% (30/30 statements)
/workspace/microservices/broker/api/handlers/v3/user/account/notifications/repository.go        : 100.00% (10/10 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/handler.go                    : 100.00% (7/7 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/mocks.go                      : 100.00% (17/17 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/shared.go                     : 100.00% (52/52 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/useraccess.go                 : 100.00% (56/56 statements)
/workspace/microservices/broker/api/handlers/v3/user/instruction/handler.go                     : 100.00% (69/69 statements)
/workspace/microservices/broker/api/handlers/v3/user/profile/handler.go                         : 100.00% (34/34 statements)
/workspace/microservices/broker/api/routes/routes.go                                            : 100.00% (25/25 statements)
/workspace/microservices/broker/api/synapse/purge_expired.go                                    : 100.00% (17/17 statements)
/workspace/microservices/broker/api/synapse/route.go                                            : 100.00% (7/7 statements)
/workspace/microservices/broker/main.go                                                         : 97.44% (38/39 statements)
/workspace/microservices/coordinator/internal/setup/bigquery.go                                 : 0.00% (0/20 statements)
/workspace/microservices/coordinator/internal/setup/postgres.go                                 : 0.00% (0/1 statements)
/workspace/microservices/coordinator/internal/setup/pubsub.go                                   : 0.00% (0/28 statements)
/workspace/microservices/coordinator/internal/setup/redis.go                                    : 0.00% (0/24 statements)
/workspace/microservices/coordinator/main.go                                                    : 0.00% (0/42 statements)
/workspace/microservices/etl/main.go                                                            : 97.92% (47/48 statements)
/workspace/microservices/etl/processors/handlers/dlq/batch/handler.go                           : 100.00% (30/30 statements)
/workspace/microservices/etl/processors/handlers/dlq/messages/handler.go                        : 100.00% (35/35 statements)
/workspace/microservices/etl/processors/handlers/etlShared/shared.go                            : 56.52% (26/46 statements)
/workspace/microservices/etl/processors/handlers/gateway/faultLogs/handler.go                   : 100.00% (95/95 statements)
/workspace/microservices/etl/processors/handlers/gateway/faultNotification/handler.go           : 100.00% (69/69 statements)
/workspace/microservices/etl/processors/handlers/gateway/gatewayLog/handler.go                  : 100.00% (63/63 statements)
/workspace/microservices/etl/processors/handlers/gateway/helper/helper.go                       : 0.00% (0/22 statements)
/workspace/microservices/etl/processors/handlers/gateway/macAddress/handler.go                  : 100.00% (80/80 statements)
/workspace/microservices/etl/processors/handlers/gateway/monitorName/handler.go                 : 100.00% (84/84 statements)
/workspace/microservices/etl/processors/handlers/gateway/perfStats/handler.go                   : 100.00% (51/51 statements)
/workspace/microservices/etl/processors/handlers/gateway/rmsData/handler.go                     : 100.00% (59/59 statements)
/workspace/microservices/etl/processors/handlers/gateway/rmsEngine/handler.go                   : 100.00% (84/84 statements)
/workspace/microservices/etl/processors/handlers/notifications/handler.go                       : 100.00% (77/77 statements)
/workspace/microservices/etl/processors/handlers/notifications/twilio/client.go                 : 100.00% (29/29 statements)
/workspace/microservices/etl/processors/handlers/notifications/twilio/service.go                : 100.00% (29/29 statements)
/workspace/microservices/etl/processors/handlers/raw/handler.go                                 : 100.00% (32/32 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/cache_repository.go       : 100.00% (40/40 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/handler.go                : 100.00% (17/17 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/persistence_repository.go : 100.00% (37/37 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/service.go                : 100.00% (121/121 statements)
/workspace/microservices/etl/processors/subscriptions/manager.go                                : 100.00% (63/63 statements)
/workspace/microservices/etl/processors/subscriptions/schema.go                                 : 100.00% (1/1 statements)
/workspace/microservices/onramp/handlers/assets/envJs.go                                        : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/configGateway.go                                       : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/devices.go                                             : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/foo.go                                                 : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/gateway.go                                             : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/organizations.go                                       : 100.00% (3/3 statements)
/workspace/microservices/onramp/main.go                                                         : 97.50% (39/40 statements)
/workspace/microservices/onramp/routes.go                                                       : 77.52% (100/129 statements)
/workspace/microservices/testing/utils/utils.go                                                 : 77.27% (17/22 statements)
/workspace/shared/api/authorizer/authorizer.go                                                  : 100.00% (227/227 statements)
/workspace/shared/api/handlers/defaultapi/handler.go                                            : 100.00% (1/1 statements)
/workspace/shared/api/helper/helper.go                                                          : 95.45% (21/22 statements)
/workspace/shared/api/jwttokens/general.go                                                      : 100.00% (8/8 statements)
/workspace/shared/api/jwttokens/generate.go                                                     : 0.00% (0/14 statements)
/workspace/shared/api/jwttokens/jwttokens.go                                                    : 72.62% (61/84 statements)
/workspace/shared/api/middleware/bqbatch.go                                                     : 100.00% (4/4 statements)
/workspace/shared/api/middleware/connections.go                                                 : 100.00% (4/4 statements)
/workspace/shared/api/middleware/jwtauthorizer.go                                               : 100.00% (12/12 statements)
/workspace/shared/api/middleware/synapse_auth.go                                                : 100.00% (7/7 statements)
/workspace/shared/api/response/response.go                                                      : 100.00% (25/25 statements)
/workspace/shared/api/security/certificates.go                                                  : 100.00% (17/17 statements)
/workspace/shared/api/security/firebase.go                                                      : 100.00% (23/23 statements)
/workspace/shared/api/security/hash.go                                                          : 100.00% (4/4 statements)
/workspace/shared/api/security/secrets.go                                                       : 100.00% (7/7 statements)
/workspace/shared/bqbatch/bqbatch.go                                                            : 69.84% (132/189 statements)
/workspace/shared/connect/bigquery.go                                                           : 99.55% (220/221 statements)
/workspace/shared/connect/connect.go                                                            : 78.99% (109/138 statements)
/workspace/shared/connect/firestore.go                                                          : 97.62% (41/42 statements)
/workspace/shared/connect/getReleaseIdentifier.go                                               : 100.00% (13/13 statements)
/workspace/shared/connect/postgres.go                                                           : 96.37% (186/193 statements)
/workspace/shared/connect/pubsub.go                                                             : 100.00% (46/46 statements)
/workspace/shared/connect/redis.go                                                              : 100.00% (48/48 statements)
/workspace/shared/devices/devices.go                                                            : 100.00% (101/101 statements)
/workspace/shared/devices/edi/edicmu2212/edicmu2212.go                                          : 99.51% (405/407 statements)
/workspace/shared/devices/edi/edicmu2212/logFaultSignalSequence.go                              : 100.00% (53/53 statements)
/workspace/shared/devices/edi/edicmu2212/logPreviousFail.go                                     : 100.00% (147/147 statements)
/workspace/shared/devices/edi/edicmu2212/utilities.go                                           : 100.00% (5/5 statements)
/workspace/shared/devices/edi/ediecl2010/logACLineEvent.go                                      : 100.00% (27/27 statements)
/workspace/shared/devices/edi/ediecl2010/logConfiguration.go                                    : 100.00% (133/133 statements)
/workspace/shared/devices/edi/ediecl2010/logFaultSignalSequence.go                              : 100.00% (53/53 statements)
/workspace/shared/devices/edi/ediecl2010/logMonitorReset.go                                     : 100.00% (19/19 statements)
/workspace/shared/devices/edi/ediecl2010/logPreviousFail.go                                     : 100.00% (160/160 statements)
/workspace/shared/devices/edi/ediecl2010/monitorIDandName.go                                    : 100.00% (16/16 statements)
/workspace/shared/devices/edi/ediecl2010/rmsEngineData.go                                       : 100.00% (17/17 statements)
/workspace/shared/devices/edi/ediecl2010/rmsStatus.go                                           : 100.00% (73/73 statements)
/workspace/shared/devices/edi/ediecl2010/utilities.go                                           : 100.00% (14/14 statements)
/workspace/shared/devices/edi/ediecl2018/ediecl2018.go                                          : 100.00% (33/33 statements)
/workspace/shared/devices/edi/edimmu16le/edimmu16le.go                                          : 100.00% (1013/1013 statements)
/workspace/shared/devices/edi/helper/bq_converter.go                                            : 100.00% (78/78 statements)
/workspace/shared/devices/edi/helper/helper.go                                                  : 100.00% (410/410 statements)
/workspace/shared/devices/helper/helper.go                                                      : 100.00% (25/25 statements)
/workspace/shared/healthz/healthz.go                                                            : 100.00% (42/42 statements)
/workspace/shared/httplogger/httplogger.go                                                      : 92.86% (13/14 statements)
/workspace/shared/logger/logger.go                                                              : 100.00% (73/73 statements)
/workspace/shared/mocks/bqbatcher/bqbatcher.go                                                  : 100.00% (19/19 statements)
/workspace/shared/mocks/bqexecutor/bigQueryExecutor.go                                          : 100.00% (3/3 statements)
/workspace/shared/mocks/dbexecutor/dbExecutor.go                                                : 100.00% (50/50 statements)
/workspace/shared/mocks/firestore/firestore.go                                                  : 100.00% (80/80 statements)
/workspace/shared/mocks/healthz/healthz.go                                                      : 100.00% (9/9 statements)
/workspace/shared/mocks/mock.go                                                                 : 100.00% (3/3 statements)
/workspace/shared/mocks/notifications/notifications.go                                          : 100.00% (3/3 statements)
/workspace/shared/mocks/pubsub/pubsub.go                                                        : 100.00% (48/48 statements)
/workspace/shared/mocks/schemaexecutor/schemaMigrationExecutor.go                               : 100.00% (34/34 statements)
/workspace/shared/pubsubdata/helper.go                                                          : 100.00% (27/27 statements)
/workspace/shared/rest/onramp/helper/helper.go                                                  : 100.00% (3/3 statements)
/workspace/shared/rest/onramp/organization/organization.go                                      : 100.00% (174/174 statements)
/workspace/shared/rest/onramp/organization/schemas.go                                           : 100.00% (1/1 statements)
/workspace/shared/rest/onramp/softwaregateway/schemas.go                                        : 100.00% (1/1 statements)
/workspace/shared/rest/onramp/softwaregateway/softwaregateway.go                                : 100.00% (199/199 statements)
/workspace/shared/schema_mgmt/mocks.go                                                          : 50.00% (2/4 statements)
/workspace/shared/schema_mgmt/schema_mgmt.go                                                    : 100.00% (227/227 statements)
/workspace/shared/util/rand.go                                                                  : 100.00% (4/4 statements)
/workspace/shared/util/test/test.go                                                             : 0.00% (0/25 statements)
-----
Overall coverage: 95.39% (7380/7737 statements)
