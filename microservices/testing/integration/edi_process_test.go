package integration

// TODO: Add tests for the EDI process when we have data to test with

// import (
// 	"encoding/base64"
// 	"testing"

// 	"github.com/stretchr/testify/require"
// 	"synapse-its.com/shared/devices"
// 	"synapse-its.com/shared/pubsubdata"
// )

// // TestProcessLogFaultSignalSequence verifies that the ProcessLogFaultSignalSequence function
// // correctly decodes a Base64 payload and parses it without error.
// func TestProcessLogFaultSignalSequence(t *testing.T) {
// 	const rawB64 = "kjkzARkAAAUPGgR3DwAAAAAAAIgAAAAAAAAA93kS8ncPAAAAAAAAiAAAAAAAAAD3eRLofw8AAAAAAACIAAAAAAAAAPd6Ecb/DwAAAAAAAAAAAAAAAAAA93oRvP8PAAAEAAAAAAAAAAAAAAD3eQ6muw8AAEQAAAAAAAAAAAAAAPd6Dpy7DwAARAAAAAQAAAAAAAAA93oKvrsPAAAAAAAARAAAAAAAAAD3egq0vw8AAAAAAABEAAAAAAAAAPd6CYj/DwAAAAAAAAAAAAAAAAAA93oJft8PAAAiAAAAAAAAAAAAAAD3egZo3Q8AACIAAAAAAAAAAAAAAPd5Bl7dDwAAAgAAACIAAAAAAAAA93oAKN0PAAAAAAAAIgAAAAAAAAD3egAe3QMAAAAAAAAiAAAAAAAAAPd5hw=="

// 	// Decode Base64 into bytes
// 	byteMsg, err := base64.StdEncoding.DecodeString(rawB64)
// 	require.NoError(t, err, "Base64 decoding should succeed")

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, err := devices.ProcessLogFaultSignalSequence(header, byteMsg)
// 	require.NoError(t, err, "ProcessLogFaultSignalSequence should not return an error")
// 	require.NotNil(t, hdr, "headerDetails should not be nil")
// 	require.NotNil(t, records, "records should not be nil")
// }

// func TestProcessLogConfiguration(t *testing.T) {
// 	const rawB64 = "lTkzARkAAAEgGAIAAKYAAAAjAACwAgAAAgAAKAAAIAAAoAAAIAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/DwAAAAAAAAAAAAAAAAAAAAAAAP8AAAD/AAAA/w8AAP8AAAD/DwAAAAAAAAAAAAAAAAAAAAAAABQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFP8PAAD/AAAA/w8AAAwAAAAAAAAAAAAAAAAA/w8AAAATkyZDCBMFJaCUCAkKCxgZAQMFBxESAgQGCBUW5w=="

// 	// Decode Base64 into bytes
// 	byteMsg, err := base64.StdEncoding.DecodeString(rawB64)
// 	require.NoError(t, err, "Base64 decoding should succeed")

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, err := devices.ProcessLogConfiguration(header, byteMsg)
// 	require.NoError(t, err, "TestProcessLogConfiguration should not return an error")
// 	require.NotNil(t, hdr, "headerDetails should not be nil")
// 	require.NotNil(t, records, "records should not be nil")
// }

// func TestProcessRMSData(t *testing.T) {
// 	const rawB64 = "kzkzARkAAAAAAAAAuw8AAAAAAABEAAAAAAAAAAAAAAAAAAAA9wB6enl6eXp6AAAAAAAAAAAAAHp6AHl6egB5enp6egAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHkAAAB5AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB8fAB8gHwAeKSo1KgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAfAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADdAFxYFJV4AersPAAAAAAAAQAAAAAAAAAAAAAAAAAAAAD8AevsA6Q=="

// 	// Decode Base64 into bytes
// 	byteMsg, err := base64.StdEncoding.DecodeString(rawB64)
// 	require.NoError(t, err, "Base64 decoding should succeed")

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "RMSData",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, err := devices.ProcessRmsData(header, byteMsg)
// 	require.NoError(t, err, "TestProcessRMSData should not return an error")
// 	require.NotNil(t, hdr, "headerDetails should not be nil")
// 	require.NotNil(t, records, "records should not be nil")
// }
