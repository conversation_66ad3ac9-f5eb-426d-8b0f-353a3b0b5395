package integration

import (
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/iterator"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/schema_mgmt"
	Utils "synapse-its.com/testing/utils"
)

// Test the GetMostRecentSchema function.
func TestGetMostRecentSchema(t *testing.T) {
	assert := assert.New(t)

	// Valid directory, valid semver directory names.
	_, err := schema_mgmt.GetMostRecentSchema("data-core-bq")
	assert.NoError(err, "should get most recent schema without error")

	// Invalid directory.
	_, err = schema_mgmt.GetMostRecentSchema("foo")
	assert.Error(err, "should return error for non-existent schema")

	// Valid directory, no semver directory names.
	_, err = schema_mgmt.GetMostRecentSchema("")
	assert.Error(err, "should return error for empty schema directory")
}

// ColumnInfo represents column metadata for comparison
type ColumnInfo struct {
	Name         string
	DataType     string
	IsNullable   string
	DefaultValue *string
}

// ConstraintInfo represents constraint metadata for comparison
type ConstraintInfo struct {
	Name             string
	Type             string
	TableName        string
	ColumnName       string
	ReferencedTable  *string
	ReferencedColumn *string
}

// PrimaryKeyInfo represents primary key metadata for comparison
type PrimaryKeyInfo struct {
	TableName  string
	ColumnName string
	Position   int
}

// getAllTables() is a helper function to get all tables in the dataset whose
// name starts with the config's namespace.  It supports multiple databases.
func getAllTables(db connect.DatabaseExecutor) []string {
	tables := []string{}
	switch dbType := db.(type) {
	case *connect.BigQueryExecutor:
		it := dbType.Client.Dataset(dbType.Config.DBName).Tables(dbType.Ctx)
		for {
			tbl, err := it.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				return nil
			}
			if strings.HasPrefix(tbl.TableID, dbType.Config.Namespace) {
				tables = append(tables, tbl.TableID)
			}
		}

	case *connect.PostgresExecutor:
		tablesList, err := dbType.QueryGeneric("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
		if err != nil {
			return nil
		}
		for _, tbl := range tablesList {
			if strings.HasPrefix(tbl["table_name"].(string), dbType.Config.Namespace) {
				tables = append(tables, tbl["table_name"].(string))
			}
		}

	default:
		return nil
	}
	return tables
}

// getAllColumns returns column information for all tables with the given namespace prefix
func getAllColumns(db connect.DatabaseExecutor, namespace string) map[string][]ColumnInfo {
	columns := make(map[string][]ColumnInfo)

	switch dbType := db.(type) {
	case *connect.BigQueryExecutor:
		// For BigQuery, we need to get schema for each table individually
		tables := getAllTables(db)
		for _, tableName := range tables {
			if !strings.HasPrefix(tableName, namespace) {
				continue
			}

			table := dbType.Client.Dataset(dbType.Config.DBName).Table(tableName)
			metadata, err := table.Metadata(dbType.Ctx)
			if err != nil {
				continue
			}

			var tableColumns []ColumnInfo
			for _, field := range metadata.Schema {
				col := ColumnInfo{
					Name:       field.Name,
					DataType:   string(field.Type),
					IsNullable: "YES", // BigQuery fields are nullable by default unless required
				}
				if field.Required {
					col.IsNullable = "NO"
				}
				tableColumns = append(tableColumns, col)
			}
			columns[tableName] = tableColumns
		}

	case *connect.PostgresExecutor:
		query := `
			SELECT 
				table_name,
				column_name,
				data_type,
				is_nullable,
				column_default
			FROM information_schema.columns 
			WHERE table_schema = 'public' 
			AND table_name LIKE $1
			ORDER BY table_name, ordinal_position
		`

		rows, err := dbType.QueryGeneric(query, namespace+"%")
		if err != nil {
			return columns
		}

		for _, row := range rows {
			tableName := row["table_name"].(string)
			col := ColumnInfo{
				Name:       row["column_name"].(string),
				DataType:   row["data_type"].(string),
				IsNullable: row["is_nullable"].(string),
			}
			if defaultVal := row["column_default"]; defaultVal != nil {
				if str, ok := defaultVal.(string); ok {
					col.DefaultValue = &str
				}
			}
			columns[tableName] = append(columns[tableName], col)
		}

	default:
		return columns
	}
	return columns
}

// getAllConstraints returns constraint information for all tables with the given namespace prefix
func getAllConstraints(db connect.DatabaseExecutor, namespace string) []ConstraintInfo {
	var constraints []ConstraintInfo

	switch dbType := db.(type) {
	case *connect.BigQueryExecutor:
		// BigQuery doesn't have traditional constraints like foreign keys in the same way
		// We would need to implement this differently for BigQuery if needed
		return constraints

	case *connect.PostgresExecutor:
		query := `
			SELECT DISTINCT
				tc.constraint_name,
				tc.constraint_type,
				tc.table_name,
				kcu.column_name,
				ccu.table_name AS referenced_table,
				ccu.column_name AS referenced_column
			FROM information_schema.table_constraints tc
			LEFT JOIN information_schema.key_column_usage kcu
				ON tc.constraint_name = kcu.constraint_name
				AND tc.table_schema = kcu.table_schema
			LEFT JOIN information_schema.constraint_column_usage ccu
				ON tc.constraint_name = ccu.constraint_name
				AND tc.table_schema = ccu.table_schema
			WHERE tc.table_schema = 'public'
			AND tc.table_name LIKE $1
			ORDER BY tc.table_name, tc.constraint_name
		`

		rows, err := dbType.QueryGeneric(query, namespace+"%")
		if err != nil {
			return constraints
		}

		for _, row := range rows {
			constraint := ConstraintInfo{
				Name:      row["constraint_name"].(string),
				Type:      row["constraint_type"].(string),
				TableName: row["table_name"].(string),
			}

			// Handle nullable column_name
			if colName := row["column_name"]; colName != nil {
				if str, ok := colName.(string); ok {
					constraint.ColumnName = str
				}
			}

			if refTable := row["referenced_table"]; refTable != nil {
				if str, ok := refTable.(string); ok {
					constraint.ReferencedTable = &str
				}
			}
			if refCol := row["referenced_column"]; refCol != nil {
				if str, ok := refCol.(string); ok {
					constraint.ReferencedColumn = &str
				}
			}
			constraints = append(constraints, constraint)
		}

	default:
		return constraints
	}
	return constraints
}

// getAllPrimaryKeys returns primary key information for all tables with the given namespace prefix
func getAllPrimaryKeys(db connect.DatabaseExecutor, namespace string) []PrimaryKeyInfo {
	var primaryKeys []PrimaryKeyInfo

	switch dbType := db.(type) {
	case *connect.BigQueryExecutor:
		// BigQuery doesn't have traditional primary keys
		// We would need to implement this differently for BigQuery if needed
		return primaryKeys

	case *connect.PostgresExecutor:
		query := `
			SELECT 
				tc.table_name,
				kcu.column_name,
				kcu.ordinal_position
			FROM information_schema.table_constraints tc
			JOIN information_schema.key_column_usage kcu
				ON tc.constraint_name = kcu.constraint_name
				AND tc.table_schema = kcu.table_schema
			WHERE tc.constraint_type = 'PRIMARY KEY'
			AND tc.table_schema = 'public'
			AND tc.table_name LIKE $1
			ORDER BY tc.table_name, kcu.ordinal_position
		`

		rows, err := dbType.QueryGeneric(query, namespace+"%")
		if err != nil {
			return primaryKeys
		}

		for _, row := range rows {
			pk := PrimaryKeyInfo{
				TableName:  row["table_name"].(string),
				ColumnName: row["column_name"].(string),
				Position:   int(row["ordinal_position"].(int64)),
			}
			primaryKeys = append(primaryKeys, pk)
		}

	default:
		return primaryKeys
	}
	return primaryKeys
}

// Test the GetSchemaVersion function (the happy path).
func TestApplyMigrations_Happy(t *testing.T) {
	const (
		version         = ""
		expectedVersion = schema_mgmt.SCHEMA_VERSION_MAX
	)

	baseConfig := connect.DatabaseConfig{
		Environment: "dev",
		Namespace:   "T_SM_HAPPY",
	}

	tests := []struct {
		name       string
		schemaName string
		makeDB     func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error)
	}{
		{
			name:       "BigQuery",
			schemaName: "data-core-bq",
			makeDB: func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error) {
				return connect.BigQuery(t.Context(), cfg, nil)
			},
		},
		{
			name:       "Postgres",
			schemaName: "data-core-pg",
			makeDB: func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error) {
				cfg.DBName = os.Getenv("POSTGRES_DB")
				return connect.Postgres(t.Context(), cfg)
			},
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			require := require.New(t)

			// Create a copy of the base config for each test case
			cfg := baseConfig

			// 1) connect
			db, err := tc.makeDB(t, &cfg)
			require.NoError(err, "connect to %s", tc.name)

			// 2) before migrations: expect no tables
			pre := getAllTables(db)
			assert.Empty(pre, "no tables before migrations for %s", tc.name)

			// 3) apply migrations
			start := time.Now()
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, version))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, version))
			}

			// 4) inspect first migration row
			rows, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
				LIMIT 1
			`)
			require.NoError(err)
			require.Len(rows, 1)

			first := rows[0]
			assert.Equal(0.0, first["sequence"], "sequence==0")
			assert.Equal("", first["md5_hash"], "md5_hash empty")

			uf, ok := first["update_file"].(string)
			require.True(ok, "update_file is string")
			assert.True(strings.HasPrefix(uf, "SCHEMA_CREATION "), "prefix")

			parts := strings.Fields(uf)
			assert.Len(parts, 4, "update_file parts")

			kv := map[string]string{}
			for _, seg := range parts[1:] {
				pair := strings.SplitN(seg, ":", 2)
				require.Len(pair, 2, "segment %q", seg)
				kv[pair[0]] = pair[1]
			}

			assert.Equal(tc.schemaName, kv["SCHEMA"], "SCHEMA")
			assert.Equal(expectedVersion, kv["VERSION_DESIRED"], "VERSION_DESIRED")
			assert.Equal(cfg.Environment, kv["ENVIRONMENT"], "ENVIRONMENT")

			at, ok := first["applied_at"].(time.Time)
			require.True(ok, "applied_at is time.Time")
			assert.True(at.After(start) && at.Before(time.Now()), "applied_at window")

			// 5) idempotency
			beforeTables := getAllTables(db)
			require.NotEmpty(beforeTables, "tables exist after first run")

			beforeMigs, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
			`)
			require.NoError(err)

			// run migrations again
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, version))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, version))
			}

			afterTables := getAllTables(db)
			assert.ElementsMatch(beforeTables, afterTables, "tables unchanged")

			afterMigs, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
			`)
			require.NoError(err)
			assert.Equal(len(beforeMigs), len(afterMigs), "migration count unchanged")
			assert.Equal(beforeMigs, afterMigs, "migration rows unchanged")
		})
	}
}

// TestUpgradeExisting tests the upgrade from the first version of the schema
// (e.g., "0.0") to the most recent without error.
func TestUpgradeExisting(t *testing.T) {
	const (
		earliestVersion = "0.0"
		expectedVersion = schema_mgmt.SCHEMA_VERSION_MAX
	)

	baseConfig := connect.DatabaseConfig{
		Environment: "dev",
		Namespace:   "T_SM_UPGRADE",
	}

	tests := []struct {
		name       string
		schemaName string
		makeDB     func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error)
	}{
		{
			name:       "BigQuery",
			schemaName: "data-core-bq",
			makeDB: func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error) {
				return connect.BigQuery(t.Context(), cfg, nil)
			},
		},
		{
			name:       "Postgres",
			schemaName: "data-core-pg",
			makeDB: func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error) {
				cfg.DBName = os.Getenv("POSTGRES_DB")
				return connect.Postgres(t.Context(), cfg)
			},
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			require := require.New(t)

			// Create a copy of the base config for each test case
			cfg := baseConfig

			// 1) connect
			db, err := tc.makeDB(t, &cfg)
			require.NoError(err, "connect to %s", tc.name)

			// 2) before migrations: expect no tables
			pre := getAllTables(db)
			assert.Empty(pre, "no tables before migrations for %s", tc.name)

			// 3) apply migrations for the base schema
			start := time.Now()
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, "0.0"))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, "0.0"))
			}

			// 4) inspect first migration row
			rows, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
				LIMIT 1
			`)
			require.NoError(err)
			require.Len(rows, 1)

			first := rows[0]
			assert.Equal(0.0, first["sequence"], "sequence==0")
			assert.Equal("", first["md5_hash"], "md5_hash empty")

			uf, ok := first["update_file"].(string)
			require.True(ok, "update_file is string")
			assert.True(strings.HasPrefix(uf, "SCHEMA_CREATION "), "prefix")

			parts := strings.Fields(uf)
			assert.Len(parts, 4, "update_file parts")

			kv := map[string]string{}
			for _, seg := range parts[1:] {
				pair := strings.SplitN(seg, ":", 2)
				require.Len(pair, 2, "segment %q", seg)
				kv[pair[0]] = pair[1]
			}

			assert.Equal(tc.schemaName, kv["SCHEMA"], "SCHEMA")
			assert.Equal(earliestVersion, kv["VERSION_DESIRED"], "VERSION_DESIRED")
			assert.Equal(cfg.Environment, kv["ENVIRONMENT"], "ENVIRONMENT")

			at, ok := first["applied_at"].(time.Time)
			require.True(ok, "applied_at is time.Time")
			assert.True(at.After(start) && at.Before(time.Now()), "applied_at window")

			// 5) Upgrade to the latest.
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, expectedVersion))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, expectedVersion))
			}

			_, err = db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
			`)
			require.NoError(err)

			// 6) Verify idempotency
			beforeTables := getAllTables(db)
			require.NotEmpty(beforeTables, "tables exist after first run")
			beforeMigsCount, err := db.QueryGeneric(`
				SELECT COUNT(*) AS count
				FROM {{schema_migrations}}
			`)
			require.NoError(err)
			require.NotZero(beforeMigsCount[0]["count"], "migration count before upgrade is not zero")

			// run migrations again
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, expectedVersion))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, expectedVersion))
			}
			afterTables := getAllTables(db)
			assert.ElementsMatch(beforeTables, afterTables, "tables unchanged after upgrade")
			afterMigsCount, err := db.QueryGeneric(`
				SELECT COUNT(*) AS count
				FROM {{schema_migrations}}
			`)
			require.NoError(err)
			require.Equal(beforeMigsCount[0]["count"], afterMigsCount[0]["count"], "migration count unchanged after upgrade")

			// 7) Check schema against DEV namespace
			devConfig := connect.DatabaseConfig{
				Environment: "dev",
				Namespace:   "DEV",
			}

			devDB, err := tc.makeDB(t, &devConfig)
			require.NoError(err, "connect to DEV namespace")

			devTables := getAllTables(devDB)
			upgradeTables := getAllTables(db)

			// Compare table counts
			assert.Equal(len(devTables), len(upgradeTables), "table count should match DEV namespace")

			// Compare table names (excluding namespace prefix)
			devTableNames := make([]string, len(devTables))
			upgradeTableNames := make([]string, len(upgradeTables))

			for i, table := range devTables {
				devTableNames[i] = strings.TrimPrefix(table, devConfig.Namespace+"_")
			}
			for i, table := range upgradeTables {
				upgradeTableNames[i] = strings.TrimPrefix(table, cfg.Namespace+"_")
			}

			assert.ElementsMatch(devTableNames, upgradeTableNames, "table names should match DEV namespace")

			// Compare column structures
			devColumns := getAllColumns(devDB, devConfig.Namespace)
			upgradeColumns := getAllColumns(db, cfg.Namespace)

			// Normalize column maps by removing namespace prefixes
			devColumnsNormalized := make(map[string][]ColumnInfo)
			upgradeColumnsNormalized := make(map[string][]ColumnInfo)

			for tableName, columns := range devColumns {
				normalizedTableName := strings.TrimPrefix(tableName, devConfig.Namespace+"_")
				devColumnsNormalized[normalizedTableName] = columns
			}

			for tableName, columns := range upgradeColumns {
				normalizedTableName := strings.TrimPrefix(tableName, cfg.Namespace+"_")
				upgradeColumnsNormalized[normalizedTableName] = columns
			}

			// Compare columns for each table
			for tableName := range devColumnsNormalized {
				devCols := devColumnsNormalized[tableName]
				upgradeCols := upgradeColumnsNormalized[tableName]

				assert.Equal(len(devCols), len(upgradeCols), "column count should match for table %s", tableName)

				// Create maps for easier comparison
				devColMap := make(map[string]ColumnInfo)
				upgradeColMap := make(map[string]ColumnInfo)

				for _, col := range devCols {
					devColMap[col.Name] = col
				}
				for _, col := range upgradeCols {
					upgradeColMap[col.Name] = col
				}

				// Compare each column
				for colName, devCol := range devColMap {
					upgradeCol, exists := upgradeColMap[colName]
					assert.True(exists, "column %s should exist in upgraded table %s", colName, tableName)
					if exists {
						assert.Equal(devCol.DataType, upgradeCol.DataType, "data type should match for column %s.%s", tableName, colName)
						assert.Equal(devCol.IsNullable, upgradeCol.IsNullable, "nullable should match for column %s.%s", tableName, colName)
						// Note: Default values may differ due to generation differences, so we skip this comparison
					}
				}
			}

			// Compare constraints (PostgreSQL only for now)
			if tc.name == "Postgres" {
				devConstraints := getAllConstraints(devDB, devConfig.Namespace)
				upgradeConstraints := getAllConstraints(db, cfg.Namespace)

				// Normalize constraint data by removing namespace prefixes
				normalizeConstraints := func(constraints []ConstraintInfo, namespace string) map[string][]ConstraintInfo {
					normalized := make(map[string][]ConstraintInfo)
					for _, constraint := range constraints {
						normalizedTableName := strings.TrimPrefix(constraint.TableName, namespace+"_")
						normalizedConstraint := constraint
						normalizedConstraint.TableName = normalizedTableName

						// Normalize referenced table if it exists
						if constraint.ReferencedTable != nil {
							normalizedRefTable := strings.TrimPrefix(*constraint.ReferencedTable, namespace+"_")
							normalizedConstraint.ReferencedTable = &normalizedRefTable
						}

						normalized[normalizedTableName] = append(normalized[normalizedTableName], normalizedConstraint)
					}
					return normalized
				}

				devConstraintsNormalized := normalizeConstraints(devConstraints, devConfig.Namespace)
				upgradeConstraintsNormalized := normalizeConstraints(upgradeConstraints, cfg.Namespace)

				// Compare constraint counts by type for each table
				for tableName := range devConstraintsNormalized {
					devTableConstraints := devConstraintsNormalized[tableName]
					upgradeTableConstraints := upgradeConstraintsNormalized[tableName]

					// Group by constraint type
					devConstraintsByType := make(map[string]int)
					upgradeConstraintsByType := make(map[string]int)

					for _, c := range devTableConstraints {
						devConstraintsByType[c.Type]++
					}
					for _, c := range upgradeTableConstraints {
						upgradeConstraintsByType[c.Type]++
					}

					// Compare counts by type (allow some flexibility for naming differences)
					for constraintType, devCount := range devConstraintsByType {
						upgradeCount := upgradeConstraintsByType[constraintType]
						assert.Equal(devCount, upgradeCount, "constraint count should match for type %s in table %s", constraintType, tableName)
					}
				}
			}

			// Compare primary keys (PostgreSQL only for now)
			if tc.name == "Postgres" {
				devPrimaryKeys := getAllPrimaryKeys(devDB, devConfig.Namespace)
				upgradePrimaryKeys := getAllPrimaryKeys(db, cfg.Namespace)

				// Normalize primary key data by removing namespace prefixes
				devPKNormalized := make(map[string][]PrimaryKeyInfo)
				upgradePKNormalized := make(map[string][]PrimaryKeyInfo)

				for _, pk := range devPrimaryKeys {
					normalizedTableName := strings.TrimPrefix(pk.TableName, devConfig.Namespace+"_")
					normalizedPK := pk
					normalizedPK.TableName = normalizedTableName
					devPKNormalized[normalizedTableName] = append(devPKNormalized[normalizedTableName], normalizedPK)
				}

				for _, pk := range upgradePrimaryKeys {
					normalizedTableName := strings.TrimPrefix(pk.TableName, cfg.Namespace+"_")
					normalizedPK := pk
					normalizedPK.TableName = normalizedTableName
					upgradePKNormalized[normalizedTableName] = append(upgradePKNormalized[normalizedTableName], normalizedPK)
				}

				// Compare primary keys for each table
				for tableName := range devPKNormalized {
					devPKs := devPKNormalized[tableName]
					upgradePKs := upgradePKNormalized[tableName]

					assert.Equal(len(devPKs), len(upgradePKs), "primary key column count should match for table %s", tableName)

					// Sort by position for comparison
					if len(devPKs) > 0 && len(upgradePKs) > 0 {
						for i := range devPKs {
							if i < len(upgradePKs) {
								assert.Equal(devPKs[i].ColumnName, upgradePKs[i].ColumnName, "primary key column should match for table %s position %d", tableName, i+1)
								assert.Equal(devPKs[i].Position, upgradePKs[i].Position, "primary key position should match for table %s", tableName)
							}
						}
					}
				}
			}
		})
	}
}
