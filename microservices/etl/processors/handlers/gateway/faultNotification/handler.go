package faultNotification

import (
	"context"
	"fmt"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/lib/pq"
	"google.golang.org/protobuf/proto"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc     func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender               func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceDataFunc func(raw []byte) (*gatewayv1.DeviceData, error)
	BatchGetter             func(ctx context.Context) (bqbatch.Batcher, error)
	ProcessRMSFunc          func(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsStatusRecord *edihelper.RmsStatusRecord, headerDetails *edihelper.HeaderRecord, err error)
	ToBQConverter           func(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, header schemas.HeaderRecord, rawMsg []byte, status *edihelper.RmsStatusRecord) schemas.FaultNotification
	UpsertFunc              func(pg connect.DatabaseExecutor, deviceID string, rec *edihelper.RmsStatusRecord) error
	MarshalDeviceDataFunc   func(msg proto.Message) ([]byte, error)
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
	UnmarshalDevice UnmarshalDeviceDataFunc
	GetBatch        BatchGetter
	ProcessRMS      ProcessRMSFunc
	ToBQ            ToBQConverter
	UpsertDevice    UpsertFunc
	MarshalDevice   MarshalDeviceDataFunc
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		pg := connections.Postgres
		sub := connections.Pubsub.Subscription(subscriptionName)
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", subscriptionName, string(msg.ID), string(msg.Data))

			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshal protobuf message
			dd, errUm := deps.UnmarshalDevice(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling the Device Data: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Loop through each record
			unprocessedDevices := new(gatewayv1.DeviceData)
			for i, d := range dd.GetMessages() {
				logger.Debugf("Processing record : %v, Deviceid : %v", i, d.DeviceId)
				msgBytes := d.GetMessage()
				rmsStatus, rmsHeader, errDev := deps.ProcessRMS(&httpHeader, msgBytes)
				if errDev != nil {
					logger.Infof("Error parsing record: %v", errDev)
					unprocessedDevices.Messages = append(unprocessedDevices.Messages, d)
					continue
				}

				if err = deps.UpsertDevice(pg, d.DeviceId, rmsStatus); err != nil {
					logger.Errorf("Error adding message to postgres: %v", err)
					unprocessedDevices.Messages = append(unprocessedDevices.Messages, d)
					continue
				}

				// Add to the bigquery insert batch
				if err = batch.Add(deps.ToBQ(
					commonAttrs.OrganizationIdentifier,
					httpHeader.GatewayDeviceID,
					httpHeader.GatewayTimezone,
					commonAttrs.Topic,
					msg.ID,
					d.DeviceId,
					msg.PublishTime.UTC(),
					*rmsHeader.ToBigQuerySchema(),
					msgBytes,
					rmsStatus,
				)); err != nil {
					logger.Infof("Error adding message to batch: %v", err)
					unprocessedDevices.Messages = append(unprocessedDevices.Messages, d)
					continue
				}
			}

			// Send unprocessed device messages to DLQ for further investigation
			if len(unprocessedDevices.Messages) != 0 {
				logger.Warnf("Unable to process (%v) device messages", len(unprocessedDevices.Messages))
				msg.Data, err = deps.MarshalDevice(unprocessedDevices)
				if err != nil {
					logger.Errorf("Unable to marshal unprocessed messages")
					msg.Ack() // At this point consider data lost, don't try to reprocess the whole batch
					return
				}

				// Send messages to DLQ
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("unable to process (%v) device messages", len(unprocessedDevices.Messages)))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Ack() // At this point consider data lost, don't try to reprocess the whole batch
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription"+subscriptionName+": %v", err)
		}
	}
}

// upsertDeviceFault inserts or updates a DeviceFault row in one statement.
func upsertDeviceFault(pg connect.DatabaseExecutor, deviceID string, rec *edihelper.RmsStatusRecord) error {
	const query = `
			INSERT INTO {{DeviceFault}} (
				DeviceId,
				IsFaulted, 
				Fault, 
				FaultStatus,
				ChannelGreenStatus, 
				ChannelYellowStatus, 
				ChannelRedStatus,
				MonitorTime, 
				Temperature,
				VoltagesGreen, 
				VoltagesYellow, 
				VoltagesRed,
				DeviceModel
			)
			VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
			)
			ON CONFLICT (DeviceId) DO UPDATE SET
				IsFaulted           = EXCLUDED.IsFaulted,
				Fault               = EXCLUDED.Fault,
				FaultStatus         = EXCLUDED.FaultStatus,
				ChannelGreenStatus  = EXCLUDED.ChannelGreenStatus,
				ChannelYellowStatus = EXCLUDED.ChannelYellowStatus,
				ChannelRedStatus    = EXCLUDED.ChannelRedStatus,
				MonitorTime         = EXCLUDED.MonitorTime,
				Temperature         = EXCLUDED.Temperature,
				VoltagesGreen       = EXCLUDED.VoltagesGreen,
				VoltagesYellow      = EXCLUDED.VoltagesYellow,
				VoltagesRed         = EXCLUDED.VoltagesRed,
				DeviceModel         = EXCLUDED.DeviceModel;
		`

	// Retry exec in case of deadlock
	return connect.WithDeadlockRetry(func() error {
		_, err := pg.Exec(query,
			deviceID,
			rec.IsFaulted,
			rec.Fault,
			rec.FaultStatus,
			pq.Array(rec.ChannelGreenStatus),
			pq.Array(rec.ChannelYellowStatus),
			pq.Array(rec.ChannelRedStatus),
			rec.MonitorTime,
			rec.Temperature,
			pq.Array(rec.VoltagesGreen),
			pq.Array(rec.VoltagesYellow),
			pq.Array(rec.VoltagesRed),
			rec.DeviceModel)
		return err
	})
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	UnmarshalDevice: etlShared.UnmarshalDeviceData,
	GetBatch:        bqbatch.GetBatch,
	ProcessRMS:      devices.ProcessRmsData,
	ToBQ:            edihelper.RmsStatusToFaultNotification,
	UpsertDevice:    upsertDeviceFault,
	MarshalDevice:   etlShared.ProtoMarshal,
})
