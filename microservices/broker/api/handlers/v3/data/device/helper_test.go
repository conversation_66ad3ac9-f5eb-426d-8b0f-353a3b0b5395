package device

import (
	"reflect"
	"testing"
	"time"

	helper "synapse-its.com/shared/devices/edi/helper"
)

func TestAddRedisToPayload(t *testing.T) {
	baseTime := time.Date(2021, time.February, 3, 4, 5, 6, 7, time.UTC)
	header := &helper.HeaderRecord{
		Model:            123,
		FirmwareRevision: 11,
		FirmwareVersion:  22,
		CommVersion:      33,
	}
	status := &helper.RmsStatusRecord{
		MonitorTime: baseTime,
		Fault:       "faulty",
	}
	dp := &dataPayload{
		Metadata: deviceMetadata{},
		Status:   deviceStatus{},
	}

	addRedisToPayload(dp, header, status)

	if dp.Metadata.Model != "123" {
		t.Errorf("Model = %q; want %q", dp.Metadata.Model, "123")
	}
	if dp.Metadata.FirmwareType != "11" {
		t.Errorf("FirmwareType = %q; want %q", dp.Metadata.FirmwareType, "11")
	}
	if dp.Metadata.FirmwareVersion != "22" {
		t.Errorf("FirmwareVersion = %q; want %q", dp.Metadata.FirmwareVersion, "22")
	}
	if dp.Metadata.CommVersion != "33" {
		t.Errorf("CommVersion = %q; want %q", dp.Metadata.CommVersion, "33")
	}
	wantTime := baseTime.UTC().Format(time.RFC3339Nano)
	if dp.Status.HeartbeatReceivedUTC != wantTime {
		t.Errorf("HeartbeatReceivedUTC = %q; want %q", dp.Status.HeartbeatReceivedUTC, wantTime)
	}
	if dp.Status.State != "faulty" {
		t.Errorf("State = %q; want %q", dp.Status.State, "faulty")
	}
}

func TestConvertPgDeviceInfos(t *testing.T) {
	dt := time.Date(2022, time.January, 2, 3, 4, 5, 6, time.UTC)
	mt := time.Date(2022, time.February, 3, 4, 5, 6, 7, time.UTC)
	db := []pgDeviceInfo{
		{
			MonitorTime:               mt,
			FaultStatus:               "error1",
			ChannelGreenStatus:        []bool{true, false},
			ChannelYellowStatus:       []bool{false, true},
			ChannelRedStatus:          []bool{},
			ID:                        1,
			DeviceID:                  "dev1",
			Latitude:                  "10.1",
			Longitude:                 "20.2",
			IPAddress:                 "*******",
			Port:                      "8080",
			MonitorID:                 7,
			MonitorName:               "mon1",
			EngineVersion:             2,
			EngineRevision:            3,
			DateUploadedUTC:           dt,
			SoftwareGatewayIdentifier: "sgw",
		},
	}
	tests := []struct {
		name    string
		input   *[]pgDeviceInfo
		wantNil bool
		wantLen int
		want0   dataPayload
	}{
		{name: "nil input", input: nil, wantNil: true},
		{name: "empty slice", input: &[]pgDeviceInfo{}, wantNil: false, wantLen: 0},
		{
			name: "one element", input: &db, wantNil: false, wantLen: 1,
			want0: dataPayload{
				DeviceID:         1,
				DeviceIdentifier: "dev1",
				Location: location{
					Latitude:  "10.1",
					Longitude: "20.2",
				},
				Status: deviceStatus{
					State:                "",
					HeartbeatReceivedUTC: "",
					LogUploadedUTC:       dt.Format(time.RFC3339Nano),
					LastFaultReason:      "error1",
					LastFaultUploadedUTC: mt.Format(time.RFC3339Nano),
					FaultedChannelStatus: channelStatus{
						ChannelRed:    []bool{},
						ChannelYellow: []bool{false, true},
						ChannelGreen:  []bool{true, false},
					},
				},
				Metadata: deviceMetadata{
					Manufacturer:            "",
					Model:                   "",
					UserAssignedDeviceID:    "7",
					UserAssignedDeviceName:  "mon1",
					ApplicationVersion:      "",
					FirmwareType:            "",
					FirmwareVersion:         "",
					CommVersion:             "",
					RmsEngineFirmwareType:   "3",
					RmsEngineFirmwareVerson: "2",
					IPAddress:               "*******",
					IPort:                   "8080",
				},
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := convertPgDeviceInfos(tc.input)
			if tc.wantNil {
				if got != nil {
					t.Errorf("convertPgDeviceInfos(%v) = %v; want nil", tc.input, got)
				}
				return
			}
			if got == nil {
				t.Errorf("convertPgDeviceInfos(%v) returned nil; want non-nil", tc.input)
				return
			}
			if len(*got) != tc.wantLen {
				t.Errorf("len = %d; want %d", len(*got), tc.wantLen)
				return
			}
			if tc.wantLen > 0 {
				if !reflect.DeepEqual((*got)[0], tc.want0) {
					t.Errorf("element[0] = %+v; want %+v", (*got)[0], tc.want0)
				}
			}
		})
	}
}
