package ediecl2018

import (
	"bytes"
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

type EDIECL2018 struct{}

const (
	DeviceModel          = "EDIECL2018"
	HeaderLength         = 7
	ChecksumLength       = 1
	RMSEngineRecordCount = 2
	RMSEngineDataLength  = 10

	// Version record
	RMSEngineVersionRecordStartOffset = 0
	RMSEngineVersionOffset            = 8

	// Revision record
	RMSEngineRevisionRecordStartOffset = 10
	RMSEngineRevisionOffset            = RMSEngineVersionOffset + RMSEngineDataLength

	K2018IDNameLength           = 40
	K2018IDNameLengthFirmware27 = 42
	MonitorIDlsByteOffset       = 5
	MonitorIDmsByteOffset       = 6
	MonitorNameStartOffset      = HeaderLength + 2
)

/*
Byte Message Format
===================

+-------------------------------+
|RMS Engine Version (10 bytes)  |
+-------------------------------+
|RMS Engine Revision (10 bytes) |
+-------------------------------+

*/

// RMSEngineData parses the rms engine data from the byte message
func (device EDIECL2018) RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsEngineDetail *helper.RmsEngineRecord, err error) {
	rmsEngineDetail = &helper.RmsEngineRecord{
		DeviceModel: DeviceModel,
	}

	byteMessageLength := RMSEngineRecordCount * RMSEngineDataLength
	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	err = helper.ValidateChecksum(byteMsg[RMSEngineVersionRecordStartOffset : RMSEngineVersionRecordStartOffset+RMSEngineDataLength])
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	err = helper.ValidateChecksum(byteMsg[RMSEngineRevisionRecordStartOffset : RMSEngineRevisionRecordStartOffset+RMSEngineDataLength])
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	versionHeader := byteMsg[RMSEngineVersionRecordStartOffset : RMSEngineVersionRecordStartOffset+HeaderLength]
	revisionHeader := byteMsg[RMSEngineRevisionRecordStartOffset : RMSEngineRevisionRecordStartOffset+HeaderLength]
	if !bytes.Equal(versionHeader, revisionHeader) {
		return nil, fmt.Errorf("%w : %v", helper.ErrRMSEngineDataHeaderMismatch, err)
	}

	rmsEngineDetail.EngineVersion = int64(byteMsg[RMSEngineVersionOffset])
	rmsEngineDetail.EngineRevision = int64(byteMsg[RMSEngineRevisionOffset])

	return rmsEngineDetail, nil
}

/*
Byte Message Format
===================

+-------------------------------+
|Header (7 bytes)               |
+-------------------------------+
|Monitor name (40/42 bytes)     |
+-------------------------------+
|Checksum (1 byte)              |
+-------------------------------+

Header (7 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Monitor ID ls byte
[6] Monitor ID ms byte

*/

// MonitorIDandName parses the monitor ID and name from the byte message
func (device EDIECL2018) MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (monitor *helper.MonitorNameAndId, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	byteMessageLength := HeaderLength + K2018IDNameLength + ChecksumLength
	monitorID := 0
	monitorName := ""
	if int64(header.CommVersion) >= 0x27 && int64(header.FirmwareRevision) > 0x50 {
		byteMessageLength = HeaderLength + K2018IDNameLengthFirmware27 + ChecksumLength
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	if int64(header.CommVersion) >= 0x27 && int64(header.FirmwareRevision) > 0x50 {
		monitorID = int(helper.ConvertLSandMStoUnint16(byteMsg[MonitorIDlsByteOffset], byteMsg[MonitorIDmsByteOffset]))
		monitorName = helper.GetMonitorName(byteMsg[MonitorNameStartOffset : byteMessageLength-ChecksumLength])
	}

	monitor = &helper.MonitorNameAndId{
		DeviceModel: DeviceModel,
		MonitorId:   int64(monitorID),
		MonitorName: monitorName,
	}

	return monitor, nil
}
