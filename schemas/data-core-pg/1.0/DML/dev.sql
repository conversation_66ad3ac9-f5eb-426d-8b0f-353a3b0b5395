-- Updated dev data for restructured schema
-- Additional test data for comprehensive permission testing

-- Organization and data for local gateway testing
INSERT INTO {{Organization}} VALUES 
  ('c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Synapse-Plano-Demo', 'Synapse-Plano-Demo', 'municipality', false, '2025-06-30 21:34:54.249516', '2025-06-30 21:34:54.249516');
INSERT INTO {{CustomRole}} VALUES 
  ('12dc75d1-13ac-5bbe-810c-0c40bcddca21', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_admin', 'municipality', 'Admin', 'Administrative permissions', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751'),
  ('49f5bc0b-f26f-5ab6-96a0-10f2c177a7b6', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_manager', 'municipality', 'Manager', 'Manager permissions', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751'),
  ('7a838232-87c5-519d-9147-e4f27d4f03ea', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_technician', 'municipality', 'Technician', 'Technician permissions', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751'),
  ('58925ea8-2cfb-5e7b-9fd3-d1debac814e6', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_anonymous', 'municipality', 'Anonymous', 'Deny all permissions', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751');
INSERT INTO {{SoftwareGateway}} VALUES 
  ('f55c66ea-e279-5961-a0b1-b212dca33dc9', 'localgateway', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', '2025-06-30 21:51:40', false, true, '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 60, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 43200, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": false, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}', 'Synapse-Plano-Demo', 'Synapse-Plano-Demo', '2025-06-30 21:35:16.469312', '2025-06-30 21:35:16.469312', false);
INSERT INTO {{Location}} VALUES 
  ('ced1aca1-bd42-5a04-a6fb-40feb81c5a41', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.02270000, -96.73530000, false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751'),
  ('37a51eb2-3cb0-5505-a94c-4e94ad2718d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.02270000, -96.75170000, false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751'),
  ('8c616bb0-439d-5863-a619-e50f8dd7fee4', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.01730000, -96.70260000, false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751'),
  ('b184a6bc-2eb4-58ae-a91e-10da5538065b', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.02850000, -96.70250000, false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751');
INSERT INTO {{Device}} VALUES 
  ('87d94e14-e804-58b3-9f8c-a02e5de90aeb', 7000, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'ced1aca1-bd42-5a04-a6fb-40feb81c5a41', 'Mock Device', 'NA', '127.0.0.1', 10010, 400, true, true, '2025-06-30 21:35:55.227934', '2025-06-30 21:35:55.227934', false),
  ('5a7f98cd-aaa1-5b16-be30-d2f9fb86ed74', 7001, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '37a51eb2-3cb0-5505-a94c-4e94ad2718d1', 'Mock Device', 'NA', '127.0.0.1', 10011, 400, true, true, '2025-06-30 21:35:55.227934', '2025-06-30 21:35:55.227934', false),
  ('fb2ba632-80af-5836-a82f-d7d329ef924f', 7002, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '8c616bb0-439d-5863-a619-e50f8dd7fee4', 'Mock Device', 'NA', '127.0.0.1', 10012, 400, true, true, '2025-06-30 21:35:55.227934', '2025-06-30 21:35:55.227934', false),
  ('1858185e-cc6e-5a74-ac9a-5a4b3d9a7845', 7003, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'b184a6bc-2eb4-58ae-a91e-10da5538065b', 'Mock Device', 'NA', '127.0.0.1', 10013, 400, true, true, '2025-06-30 21:35:55.227934', '2025-06-30 21:35:55.227934', false);
INSERT INTO {{DeviceGroups}} VALUES 
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'migrated group: 0a0840b7-2528-576a-94c2-6a9d31efb31f', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751');
INSERT INTO {{DeviceGroupDevices}} VALUES 
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '87d94e14-e804-58b3-9f8c-a02e5de90aeb'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '5a7f98cd-aaa1-5b16-be30-d2f9fb86ed74'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'fb2ba632-80af-5836-a82f-d7d329ef924f'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '1858185e-cc6e-5a74-ac9a-5a4b3d9a7845');
INSERT INTO {{User}} VALUES 
  ('45627c04-8d87-595a-a31b-2e675e22417a', 25, 'Demo', 'Account', '+1 832-574-2528', true, 'America/Chicago', 'Synapse - Plano Demo Account', '2025-07-01 01:31:10', false, '2025-06-30 21:36:38.067282', '2025-06-30 21:36:38.067282');
INSERT INTO {{AuthMethod}} VALUES 
  ('692d620e-7dbf-5e7b-bb66-0167c3498f51', '45627c04-8d87-595a-a31b-2e675e22417a', 'USERNAME_PASSWORD', NULL, NULL, 'SynapsePlano', '5a6fd3906328e22b6e6df22e811f8d40038a92e98520a8199183841de3889564', NULL, NULL, '2025-07-01 01:31:10', 0, true, false, false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751');
INSERT INTO {{Memberships}} VALUES 
  ('********-34a5-50f7-82d5-9039a8cf214b', '692d620e-7dbf-5e7b-bb66-0167c3498f51', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751');
INSERT INTO {{DeviceGroupRoleAssignments}} VALUES 
  ('********-34a5-50f7-82d5-9039a8cf214b', '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea', false, '2025-07-02 11:48:58.214751', '2025-07-02 11:48:58.214751');

-- Test Organizations for permission testing
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  ('550e8400-e29b-41d4-a716-446655440010', 'Test Organization 1', 'First test organization for permissions', 'municipality'),
  ('550e8400-e29b-41d4-a716-446655440020', 'Test Organization 2', 'Second test organization for permissions', 'municipality');

-- Test Users for permission testing
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  ('550e8400-e29b-41d4-a716-************', 9001, 'Admin', 'User', '19405551001', false, 'America/Chicago', 'Test admin user with full permissions', false),
  ('550e8400-e29b-41d4-a716-************', 9002, 'Manager', 'User', '19405551002', false, 'America/Chicago', 'Test manager user with limited permissions', false),
  ('550e8400-e29b-41d4-a716-************', 9003, 'Technician', 'User', '19405551003', false, 'America/Chicago', 'Test technician user with device-only permissions', false),
  ('550e8400-e29b-41d4-a716-************', 9004, 'Custom', 'User', '19405551004', false, 'America/Chicago', 'Test user with custom role permissions', false),
  ('550e8400-e29b-41d4-a716-446655440005', 9005, 'MultiOrg', 'User', '19405551005', false, 'America/Chicago', 'Test user with multiple organization access', false),
  ('550e8400-e29b-41d4-a716-446655440006', 9006, 'Anonymous', 'User', '19405551006', false, 'America/Chicago', 'Test user with anonymous role (no permissions)', false),
  ('550e8400-e29b-41d4-a716-************', 9007, 'Disabled', 'User', '19405551007', false, 'America/Chicago', 'Test disabled user', true),
  ('550e8400-e29b-41d4-a716-************', 9008, 'DeviceOnly', 'User', '19405551008', false, 'America/Chicago', 'Test user with device group permissions only', false),
  ('550e8400-e29b-41d4-a716-************', 9009, 'Reports', 'User', '19405551009', false, 'America/Chicago', 'Test user with reports permissions only', false),
-- Add Keycloak admin user
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 9999, 'Admin', 'Synapse', null, false, 'America/Chicago', 'Keycloak admin user for Synapse organization', false);

-- Test AuthMethods for permission test users
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES 
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440005', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440106', '550e8400-e29b-41d4-a716-446655440006', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440107', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', false),
  ('550e8400-e29b-41d4-a716-446655440108', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440109', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true);

-- Test Location Groups
INSERT INTO {{LocationGroups}} (Id, OrganizationId, Name) VALUES
  ('550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440010', 'Test Location Group 1'),
  ('550e8400-e29b-41d4-a716-446655440060', '550e8400-e29b-41d4-a716-446655440020', 'Test Location Group 2');

-- Test Device Groups
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  ('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440010', 'Test Device Group 1'),
  ('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440020', 'Test Device Group 2');

-- Test Memberships for permission test users
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  -- Admin user: member of both test organizations
  ('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440010'),
  ('550e8400-e29b-41d4-a716-446655440202', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440020'),
  -- Manager user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-446655440203', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440010'),
  -- Technician user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-446655440204', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440010'),
  -- Custom user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-446655440205', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440010'),
  -- Multi-org user: member of both test organizations
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440010'),
  ('550e8400-e29b-41d4-a716-446655440207', '550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440020'),
  -- Anonymous user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-446655440208', '550e8400-e29b-41d4-a716-446655440106', '550e8400-e29b-41d4-a716-446655440010'),
  -- Disabled user: member of test org 1 only (but user is disabled)
  ('550e8400-e29b-41d4-a716-446655440209', '550e8400-e29b-41d4-a716-446655440107', '550e8400-e29b-41d4-a716-446655440010'),
  -- Device-only user: member of both test organizations
  ('550e8400-e29b-41d4-a716-446655440210', '550e8400-e29b-41d4-a716-446655440108', '550e8400-e29b-41d4-a716-446655440010'),
  ('550e8400-e29b-41d4-a716-446655440211', '550e8400-e29b-41d4-a716-446655440108', '550e8400-e29b-41d4-a716-446655440020'),
  -- Reports user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-446655440212', '550e8400-e29b-41d4-a716-446655440109', '550e8400-e29b-41d4-a716-446655440010');

-- Test Custom Roles for permission testing
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description) VALUES
  -- Admin roles for test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440010', 'mun_admin', 'municipality', 'Test Org 1 Admin', 'Admin role for test organization 1'),
  ('550e8400-e29b-41d4-a716-446655440302', '550e8400-e29b-41d4-a716-446655440020', 'mun_admin', 'municipality', 'Test Org 2 Admin', 'Admin role for test organization 2'),
  -- Manager roles for test organizations
  ('550e8400-e29b-41d4-a716-446655440303', '550e8400-e29b-41d4-a716-446655440010', 'mun_manager', 'municipality', 'Test Org 1 Manager', 'Manager role for test organization 1'),
  ('550e8400-e29b-41d4-a716-446655440304', '550e8400-e29b-41d4-a716-446655440020', 'mun_manager', 'municipality', 'Test Org 2 Manager', 'Manager role for test organization 2'),
  -- Technician roles for test organizations
  ('550e8400-e29b-41d4-a716-446655440305', '550e8400-e29b-41d4-a716-446655440010', 'mun_technician', 'municipality', 'Test Org 1 Technician', 'Technician role for test organization 1'),
  ('550e8400-e29b-41d4-a716-446655440306', '550e8400-e29b-41d4-a716-446655440020', 'mun_technician', 'municipality', 'Test Org 2 Technician', 'Technician role for test organization 2'),
  -- Custom role based on technician template with overrides
  ('550e8400-e29b-41d4-a716-446655440307', '550e8400-e29b-41d4-a716-446655440010', 'mun_technician', 'municipality', 'Test Custom Role', 'Custom role with permission overrides'),
  -- Anonymous roles for test organizations
  ('550e8400-e29b-41d4-a716-446655440308', '550e8400-e29b-41d4-a716-446655440010', 'mun_anonymous', 'municipality', 'Test Org 1 Anonymous', 'Anonymous role for test organization 1'),
  ('550e8400-e29b-41d4-a716-446655440309', '550e8400-e29b-41d4-a716-446655440020', 'mun_anonymous', 'municipality', 'Test Org 2 Anonymous', 'Anonymous role for test organization 2');

-- Custom Role Permission Overrides for the custom role user
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) VALUES
  -- Override for custom role: enable org_manage_devices (normally false for technician)
  ('550e8400-e29b-41d4-a716-446655440307', 'org_manage_devices', true),
  -- Override for custom role: disable device_group_manage_devices (normally true for technician)
  ('550e8400-e29b-41d4-a716-446655440307', 'device_group_delete_devices', false);

-- Organization Role Assignments for permission test users
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  -- Admin user gets admin roles in both organizations
  ('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Admin
  ('550e8400-e29b-41d4-a716-446655440202', '550e8400-e29b-41d4-a716-446655440302'), -- Test Org 2 Admin
  -- Manager user gets manager role in test org 1
  ('550e8400-e29b-41d4-a716-446655440203', '550e8400-e29b-41d4-a716-446655440303'), -- Test Org 1 Manager
  -- Technician user gets technician role in test org 1
  ('550e8400-e29b-41d4-a716-446655440204', '550e8400-e29b-41d4-a716-446655440305'), -- Test Org 1 Technician
  -- Custom user gets custom role in test org 1
  ('550e8400-e29b-41d4-a716-446655440205', '550e8400-e29b-41d4-a716-446655440307'), -- Test Custom Role
  -- Multi-org user gets manager role in test org 1, technician role in test org 2
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-446655440303'), -- Test Org 1 Manager
  ('550e8400-e29b-41d4-a716-446655440207', '550e8400-e29b-41d4-a716-446655440306'), -- Test Org 2 Technician
  -- Anonymous user gets anonymous role in test org 1
  ('550e8400-e29b-41d4-a716-446655440208', '550e8400-e29b-41d4-a716-446655440308'), -- Test Org 1 Anonymous
  -- Disabled user gets admin role in test org 1 (but user is disabled)
  ('550e8400-e29b-41d4-a716-446655440209', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Admin
  -- Reports user gets admin role in test org 1 (but only has reports permissions via custom overrides)
  ('550e8400-e29b-41d4-a716-446655440212', '550e8400-e29b-41d4-a716-************'); -- Test Org 1 Admin

-- Device Group Role Assignments for permission test users
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  -- Admin user gets admin access to both device groups
  ('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Org 1 Admin
  ('550e8400-e29b-41d4-a716-446655440202', '550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440302'), -- Test Device Group 2, Test Org 2 Admin
  -- Manager user gets manager access to device group 1
  ('550e8400-e29b-41d4-a716-446655440203', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440303'), -- Test Device Group 1, Test Org 1 Manager
  -- Technician user gets technician access to device group 1
  ('550e8400-e29b-41d4-a716-446655440204', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440305'), -- Test Device Group 1, Test Org 1 Technician
  -- Custom user gets custom role access to device group 1
  ('550e8400-e29b-41d4-a716-446655440205', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440307'), -- Test Device Group 1, Test Custom Role
  -- Multi-org user gets manager access to device group 1 only
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440303'), -- Test Device Group 1, Test Org 1 Manager
  -- Device-only user gets technician access to both device groups (no org-level roles)
  ('550e8400-e29b-41d4-a716-446655440210', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440305'), -- Test Device Group 1, Test Org 1 Technician
  ('550e8400-e29b-41d4-a716-446655440211', '550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440306'); -- Test Device Group 2, Test Org 2 Technician

-- Location Group Role Assignments for permission test users
INSERT INTO {{LocationGroupRoleAssignments}} (MembershipId, LocationGroupId, RoleId) VALUES
  -- Admin user gets admin access to both test location groups
  ('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Org 1 Admin
  ('550e8400-e29b-41d4-a716-446655440202', '550e8400-e29b-41d4-a716-446655440060', '550e8400-e29b-41d4-a716-446655440302'), -- Test Location Group 2, Test Org 2 Admin
  -- Manager user gets manager access to location group 1
  ('550e8400-e29b-41d4-a716-446655440203', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440303'), -- Test Location Group 1, Test Org 1 Manager
  -- Technician user gets technician access to location group 1
  ('550e8400-e29b-41d4-a716-446655440204', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440305'), -- Test Location Group 1, Test Org 1 Technician
  -- Custom user gets custom role access to location group 1
  ('550e8400-e29b-41d4-a716-446655440205', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440307'), -- Test Location Group 1, Test Custom Role
  -- Multi-org user gets manager access to location group 1 only
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440303'), -- Test Location Group 1, Test Org 1 Manager
  -- Device-only user gets technician access to both location groups (but this is location-only)
  ('550e8400-e29b-41d4-a716-446655440210', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440305'), -- Test Location Group 1, Test Org 1 Technician
  ('550e8400-e29b-41d4-a716-446655440211', '550e8400-e29b-41d4-a716-446655440060', '550e8400-e29b-41d4-a716-446655440306'); -- Test Location Group 2, Test Org 2 Technician

-- Custom Role Permission Overrides for reports user (disable all except reports)
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) VALUES
  -- Disable all organization permissions for reports user except reports
  ('550e8400-e29b-41d4-a716-************', 'org_view_users', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_users', false),
  ('550e8400-e29b-41d4-a716-************', 'org_delete_users', false),
  ('550e8400-e29b-41d4-a716-************', 'org_view_settings', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_settings', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_device_groups', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_location_groups', false),
  ('550e8400-e29b-41d4-a716-************', 'org_view_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'org_delete_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'device_group_manage_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'device_group_delete_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'location_group_manage_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'location_group_delete_devices', false);

-- Note: Reports permissions (org_view_reports, org_view_admin_reports) are enabled by default in mun_admin template

---------------------------------

-- Data for the gateway developers. Do not edit unless required to!!!
-- Organizations: Updated structure with UUID Id and OrigID
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius''s Organization', 'Cornelius Dev Organization', 'municipality'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam''s Organization', 'Tam Dev Organization', 'municipality'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc''s Organization', 'Duc Dev Organization', 'municipality'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh''s Organization', 'Minh Dev Organization', 'municipality');

-- SoftwareGateways: Updated with UUID OrganizationId and MachineKey
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), 'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Cornelius - Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), 'TAMDEVGATEWAY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Tam - Dev Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), 'DUCDEVGATEWAY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Duc - Dev Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), 'MINHDEVGATEWAY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Minh - Dev Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}');

-- Locations: Create all locations before inserting devices
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_1'), 'Westheimer @ Briargreen', 'Westheimer @ Briargreen', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_2'), 'Westheimer @ Highway 6', 'Westheimer @ Highway 6', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_3'), 'Westheimer @ Westhollow Dr', 'Westheimer @ Westhollow Dr', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_5'), 'Westheimer @ Panagard Dr', 'Westheimer @ Panagard Dr', '29.7601', '-95.2701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_6'), 'Westheimer @ Windchase Blvd', 'Westheimer @ Windchase Blvd', '29.7601', '-95.6701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_7'), 'Westheimer @ Polk Rd', 'Westheimer @ Polk Rd', '29.7601', '-95.7701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_8'), 'Westheimer @ Eldridge Rd', 'Westheimer @ Eldridge Rd', '29.7601', '-95.8701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_9'), 'Westheimer @ Briargreen', 'Westheimer @ Briargreen', '29.7601', '-95.9701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10'), 'Westheimer @ Briarwest Blvd', 'Westheimer @ Briarwest Blvd', '29.7601', '-96.0701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11'), 'Westheimer @ S. Dairy Ashford', 'Westheimer @ S. Dairy Ashford', '29.7601', '-96.1701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12'), 'Westheimer @ Shadow Briar Dr', 'Westheimer @ Shadow Briar Dr', '29.7601', '-96.2701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_13'), 'Westheimer @ W Houston Center Blvd', 'Westheimer @ W Houston Center Blvd', '29.7601', '-96.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_14'), 'Westheimer @ Kirkwood', 'Westheimer @ Kirkwood', '29.7601', '-96.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_15'), 'Westheimer @ Crescent Park Dr', 'Westheimer @ Crescent Park Dr', '29.7601', '-96.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_16'), 'Westheimer @ Woodland Park Dr', 'Westheimer @ Woodland Park Dr', '29.7601', '-96.6701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_17'), 'Westheimer @ Hayes Rd', 'Westheimer @ Hayes Rd', '29.7601', '-96.7701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_18'), 'Westheimer @ Wilcrest Dr', 'Westheimer @ Wilcrest Dr', '29.7601', '-96.8701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_19'), 'Westheimer @ Walnut Bend Ln', 'Westheimer @ Walnut Bend Ln', '29.7601', '-96.9701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_20'), 'Westheimer @ Rogersdale', 'Westheimer @ Rogersdale', '29.7601', '-97.0701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_21'), 'Westheimer @ Beltway 8', 'Westheimer @ Beltway 8', '29.7601', '-97.1701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_22'), '2018KCL Plano Lab Device', '2018KCL Plano Lab Device', '32.8888', '-96.7470', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_23'), '2018KCL Mock Device', '2018KCL Mock Device', '29.7601', '-95.2701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_MMU2-16LEip_Lab'), 'Plano Lab - MMU2-16LEip', 'Plano Lab - MMU2-16LEip', '32.7767', '32.7767', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_ECL2010_TypeF+_Lab'), 'Plano Lab - ECL2010', 'Plano Lab - ECL2010', '33.0918', '-96.6989', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_CMU2212_Lab'), 'Plano Lab - CMU2212', 'Plano Lab - CMU2212', '32.8888', '-96.7470', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10000'), 'Tam - MMU2-16LEip', 'Tam - MMU2-16LEip', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10001'), 'Tam - ECL2010', 'Tam - ECL2010', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10002'), 'Tam - CMU2212', 'Tam - CMU2212', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11000'), 'Duc - MMU2-16LEip', 'Duc - MMU2-16LEip', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11001'), 'Duc - ECL2010', 'Duc - ECL2010', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11002'), 'Duc - CMU2212', 'Duc - CMU2212', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12000'), 'Minh - MMU2-16LEip', 'Minh - MMU2-16LEip', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12001'), 'Minh - ECL2010', 'Minh - ECL2010', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12002'), 'Minh - CMU2212', 'Minh - CMU2212', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99100'), 'Test Device Location 1', 'Location for test device 1', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99101'), 'Test Device Location 2', 'Location for test device 2', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99102'), 'Test Device Location 3', 'Location for test device 3', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99103'), 'Test Device Location 4', 'Location for test device 4', '29.7601', '-95.6701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'));

-- Devices: Updated structure with communication fields merged from DeviceCommunicationConfig
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, Description, Type, IpAddress, Port, FlushConnectionMs, EnableRealtime, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 1, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_1'), 'Westheimer @ Briargreen', 'Stuttgart', '127.0.0.1', 8081, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_2'), 2, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_2'), 'Westheimer @ Highway 6', 'Windows', '127.0.0.1', 8082, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), 3, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_3'), 'Westheimer @ Westhollow Dr', 'RaspberryPi', '127.0.0.1', 8083, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_5'), 5, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_5'), 'Westheimer @ Panagard Dr', 'NA', '127.0.0.1', 8085, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_6'), 6, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_6'), 'Westheimer @ Windchase Blvd', 'NA', '127.0.0.1', 8086, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_7'), 7, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_7'), 'Westheimer @ Polk Rd', 'NA', '127.0.0.1', 8087, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_8'), 8, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_8'), 'Westheimer @ Eldridge Rd', 'NA', '127.0.0.1', 8088, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_9'), 9, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_9'), 'Westheimer @ Briargreen', 'NA', '127.0.0.1', 8089, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10'), 10, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10'), 'Westheimer @ Briarwest Blvd', 'NA', '127.0.0.1', 8010, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11'), 11, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11'), 'Westheimer @ S. Dairy Ashford', 'NA', '127.0.0.1', 8011, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12'), 12, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12'), 'Westheimer @ Shadow Briar Dr', 'NA', '127.0.0.1', 8012, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_13'), 13, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_13'), 'Westheimer @ W Houston Center Blvd', 'NA', '127.0.0.1', 8013, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_14'), 14, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_14'), 'Westheimer @ Kirkwood', 'NA', '127.0.0.1', 8014, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_15'), 15, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_15'), 'Westheimer @ Crescent Park Dr', 'NA', '127.0.0.1', 8015, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_16'), 16, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_16'), 'Westheimer @ Woodland Park Dr', 'NA', '127.0.0.1', 8016, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_17'), 17, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_17'), 'Westheimer @ Hayes Rd', 'NA', '127.0.0.1', 8017, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_18'), 18, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_18'), 'Westheimer @ Wilcrest Dr', 'NA', '127.0.0.1', 8018, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_19'), 19, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_19'), 'Westheimer @ Walnut Bend Ln', 'NA', '127.0.0.1', 8019, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_20'), 20, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_20'), 'Westheimer @ Rogersdale', 'NA', '127.0.0.1', 8020, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_21'), 21, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_21'), 'Westheimer @ Beltway 8', 'NA', '127.0.0.1', 8021, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_22'), 22, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_22'), '2018KCL Plano Lab Device', 'Lab', '**********', 10001, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_23'), 23, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_23'), '2018KCL Mock Device', 'Mock', '127.0.0.1', 8084, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_MMU2-16LEip_Lab'), 5000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_MMU2-16LEip_Lab'), 'Plano Lab - MMU2-16LEip', 'Mock', '**********', 10001, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_ECL2010_TypeF+_Lab'), 5001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_ECL2010_TypeF+_Lab'), 'Plano Lab - ECL2010', 'Mock', '**********', 10001, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_CMU2212_Lab'), 5002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_CMU2212_Lab'), 'Plano Lab - CMU2212', 'Mock', '**********', 10001, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10000'), 10000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10000'), 'Tam - MMU2-16LEip', 'NA', '127.0.0.1', 8091, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10001'), 10001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10001'), 'Tam - ECL2010', 'NA', '127.0.0.1', 8092, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10002'), 10002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10002'), 'Tam - CMU2212', 'NA', '127.0.0.1', 8093, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11000'), 11000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11000'), 'Duc - MMU2-16LEip', 'NA', '127.0.0.1', 8101, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11001'), 11001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11001'), 'Duc - ECL2010', 'NA', '127.0.0.1', 8102, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11002'), 11002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11002'), 'Duc - CMU2212', 'NA', '127.0.0.1', 8103, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12000'), 12000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12000'), 'Minh - MMU2-16LEip', 'NA', '127.0.0.1', 8201, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12001'), 12001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12001'), 'Minh - ECL 2010', 'NA', '127.0.0.1', 8202, 500, true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12002'), 12002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12002'), 'Minh - CMU2212', 'NA', '127.0.0.1', 8203, 500, true, true),
  ('fdf164e6-4899-4725-bfae-5141d78887d4', 99100, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99100'), 'Device for test1', 'test1', '127.0.0.1', 9000, 500, true, true),
  ('3fbe6018-9eef-4bb6-ab9c-03663cc7b3ba', 99101, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99101'), 'Device for test2', 'test2', '127.0.0.1', 9001, 500, true, true),
  ('426b0919-812b-490d-8b48-b0ac788b9712', 99102, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99102'), 'Device for test3', 'test3', '127.0.0.1', 9002, 500, true, true),
  ('550e8400-e29b-41d4-a716-************', 99103, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99103'), 'Device for test4', 'test4', '127.0.0.1', 9003, 500, true, true);

-- Device instructions: DeviceId references Device.ID
INSERT INTO {{SoftwareGatewayInstruction}} (UserId, DeviceId, Instruction, DateQueued, DateReceived, Status) VALUES
  ('550e8400-e29b-41d4-a716-************', uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 'get_device_logs', CURRENT_TIMESTAMP, null, 'queued'),
  ('550e8400-e29b-41d4-a716-************', uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 'get_device_logs', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'received'),
  ('550e8400-e29b-41d4-a716-************', uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_2'), 'get_device_logs', CURRENT_TIMESTAMP, null, 'queued');

-- DeviceMonitorName: DeviceId now UUID, references Device.Id
INSERT INTO {{DeviceMonitorName}} (DeviceId, MonitorId, MonitorName, PubsubTimestamp, UpdatedAt) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 272, 'Test device 1', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), 999, 'Test device 2', '2025-06-01 00:00:00', '2025-06-01 00:00:00');

-- DeviceRMSEngine: DeviceId now UUID, references Device.Id
INSERT INTO {{DeviceRMSEngine}} (DeviceId, EngineVersion, EngineRevision, PubsubTimestamp, UpdatedAt) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 1, 2, '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), 3, 4, '2025-06-01 00:00:00', '2025-06-01 00:00:00');

-- DeviceLog: DeviceId now UUID, references Device.Id
INSERT INTO {{DeviceLog}} (DeviceId, DateUploaded, UserUploaded, LogId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), '2025-05-19T23:55:50Z', 1, '550e8400-e29b-41d4-a716-************'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), '2025-06-19T23:55:50Z', 1, '992e8400-e29b-41d4-a716-************');

-- Users: Updated structure with UUID Id and OrigID
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test'), 1, 'Test', 'User', '19405551234', false, 'America/Chicago', 'Primary test user'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test1'), 2, 'Test1', 'User', '19405551234', false, 'America/Chicago', 'Secondary test user');

-- UserToken: UserId references User.Id
INSERT INTO {{UserToken}} (UserId, JwtToken, JwtTokenSha256, Created, Expiration) VALUES 
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test1'), 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XFgUWHHtITccNQc-hnZRqXRkPLGz2eY0Q9SQANGCTp4Xv1oWaIZcSk_lxRe-Szd0p5kw0qe05KdMDZD2zF-zJ7-L9enMBOg-UmcdgRlogTq7TP5Is2a9EJ1KWgDCNHMvUi_u8U8qjR1Hvrn6lXBad5DfbhyTJ-HZNwAbfaAT0h_YJ73rHRCAAG0jXXo44u4m5CnHP0HhHXkGCznntsu0bgcsS5fbyqnSIhYxcXg14G2o0eh8GO3QCijr_HwcdutAjGqIL5zEBcBKceWvt4WTENHMH-0HHeMLjn1xpbXMJixm4xhvdcGH3pmDyj8lVT7uKWc6YnRPHPTf7KJdmRaLkw', 'd2cbd89168cfcf5d950ce54d85e848550c8ba9d0cbd838279e4b7adb92c49645', '2025-06-05 15:55:44.391', '2030-09-03 15:55:44.389'); 

-- AuthMethods for users
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash) VALUES 
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test1'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29');

-- AuthMethod for keycloak user
INSERT INTO {{AuthMethod}} (Id, UserId, Type, Sub, Issuer, Email, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 'OIDC', '1b08b49c-804c-4d73-98e8-b23f885a3613', 'http://keycloak:8080/realms/onramp-dev', '<EMAIL>', true);

-- Memberships: AuthMethodId references AuthMethod.Id, OrganizationId references Organization.Id
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  -- Test user memberships in all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  -- Test1 user memberships in all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  -- Keycloak admin user membership in Synapse organization
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'));

-- DeviceGroups: Create device groups for each organization
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius Dev Devices'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam Dev Devices'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc Dev Devices'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh Dev Devices');

-- LocationGroups: Create location groups for each organization
INSERT INTO {{LocationGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius Westheimer Corridor'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius Lab Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam Dev Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc Dev Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh Dev Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Test Location Group 1'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Test Location Group 2');

-- DeviceGroupDevices: Assign devices to their respective device groups
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  -- Cornelius's devices (SoftwareGateway 1)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_2')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_5')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_6')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_7')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_8')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_9')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_13')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_14')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_15')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_16')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_17')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_18')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_19')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_20')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_21')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_22')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_23')),
  -- Tam's devices (SoftwareGateway 10)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10002')),
  -- Duc's devices (SoftwareGateway 11)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11002')),
  -- Minh's devices (SoftwareGateway 12)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12002'));

-- LocationGroupLocations: Assign locations to their respective location groups
INSERT INTO {{LocationGroupLocations}} (LocationGroupId, LocationId) VALUES
  -- Cornelius Westheimer Corridor locations (main street locations)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_1')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_2')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_3')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_5')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_6')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_7')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_8')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_9')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_13')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_14')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_15')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_16')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_17')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_18')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_19')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_20')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_21')),
  -- Cornelius Lab locations (lab and test devices)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_22')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_23')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_MMU2-16LEip_Lab')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_ECL2010_TypeF+_Lab')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_CMU2212_Lab')),
  -- Tam's locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10002')),
  -- Duc's locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11002')),
  -- Minh's locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12002')),
  -- Test location groups for permission testing
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99100')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99101')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99102')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99103'));

-- CustomRoles: Create admin roles for each organization based on mun_admin template
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'mun_admin', 'municipality', 'Cornelius Org Admin', 'Admin role for Cornelius organization'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'mun_admin', 'municipality', 'Tam Org Admin', 'Admin role for Tam organization'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'mun_admin', 'municipality', 'Duc Org Admin', 'Admin role for Duc organization'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'mun_admin', 'municipality', 'Minh Org Admin', 'Admin role for Minh organization');

-- OrgRoleAssignments: Assign test users to organization admin roles
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  -- Test User (<EMAIL>) gets admin access to all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')),
  -- Test1 User (<EMAIL>) gets admin access to all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')),
  -- Keycloak admin user gets admin access to Synapse organization
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_admin'));

-- DeviceGroupRoleAssignments: Give test users access to all device groups
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  -- Test User gets admin access to all device groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')), -- Minh devices
  -- Test1 User gets admin access to all device groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')); -- Minh devices

-- LocationGroupRoleAssignments: Give test users access to location groups
INSERT INTO {{LocationGroupRoleAssignments}} (MembershipId, LocationGroupId, RoleId) VALUES
  -- Test User gets admin access to all location groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Westheimer
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Lab
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')), -- Minh locations
  -- Test1 User gets admin access to all location groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Westheimer
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Lab
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')); -- Minh locations


-- THESE MUST ALWAYS BE LAST
SELECT setval('{{Device_OrigId_SEQ}}', coalesce((select max(origid) from {{Device}}),1));
SELECT setval('{{User_OrigId_SEQ}}', coalesce((select max(origid) from {{User}}),1));
