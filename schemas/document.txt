Permissions Reference
Purpose
This document defines the permissions available in the Onramp system. Each permission includes:
•	Display Name: A human-readable label used in the UI.
•	Identifier: The internal string used in databases, APIs, and role definitions.
•	Description: A concise explanation of what access this permission grants.
This reference supports both backend enforcement and frontend display logic.
________________________________________
System Admin Permissions
Display Name	Identifier	Description
Manage All Organizations	manage_all_organizations	Grants full administrative access to all tenant organizations.
Manage All Users	manage_all_users	Grants ability to edit any user across the system.
View Reports	view_reports	Grants access to system-level or cross-organization reports.
Onboard Organizations	onboard_organizations	Allows creation and initial configuration of new organizations.
________________________________________
Organization-Level Permissions
Display Name	Identifier	Description
Manage Users	manage_users	Grants ability to create, edit, and deactivate users in the organization.
Manage Settings	manage_settings	Allows changing organization-wide settings and preferences.
Manage All Devices	manage_all_devices	Grants access to all devices in the organization.
View Reports	view_reports	Allows viewing reports scoped to this organization.
Export Reports	export_reports	Allows exporting downloadable reports.
________________________________________
Device Group Permissions
Display Name	Identifier	Description
View Device	view_device	Allows viewing assigned devices within a device group.
Update Device Status	update_device_status	Allows updating operational fields on devices in the device group.
________________________________________
Future: Location Group Permissions
Display Name	Identifier	Description
View Location Device	view_location_device	(Planned) Allows viewing devices by location.
Control Location	control_location	(Planned) Allows interaction with location zone.
________________________________________
This document will be updated as new permissions are added to the Onramp system.
________________________________________
Sample API Response: GET /users/permissions
{
  "userId": "c3c1d5f1-7ea9-4f95-a840-30a4bb3c53a0",
  "permissions": [
    {
      "scope": "global",
      "scopeId": null,
      "organizationId": null,
      "permissions": [
        "manage_all_organizations",
        "manage_all_users",
        "view_reports",
        "onboard_organizations"
      ]
    },
    {
      "scope": "org",
      "scopeId": "org-1234",
      "organizationId": "org-1234",
      "permissions": [
        "manage_users",
        "manage_settings",
        "manage_all_devices",
        "view_reports"
      ]
    },
    {
      "scope": "device_group",
      "scopeId": "group-5678",
      "organizationId": "org-1234",
      "permissions": [
        "view_device",
        "update_device_status"
      ]
    },
    {
      "scope": "device_group",
      "scopeId": "group-9012",
      "organizationId": "org-1234",
      "permissions": [
        "view_device"
      ]
    },
    {
      "scope": "location_group",
      "scopeId": "location-42",
      "organizationId": "org-1234",
      "permissions": [
        "view_location_device"
      ]
    }
  ]
}
Field Descriptions
Field	Type	Description
scope	string	One of global, org, device_group, or location_group.
scopeId	string/null	The ID of the entity to which the permissions apply.
organizationId	string/null	The associated organization for the permission scope.
permissions	array	List of granted permission identifiers in the given scope.

